/**
 * Currency Detection and Conversion System
 * Automatically detects user location and converts pricing to local currency
 */

class CurrencyDetector {
    constructor() {
        this.basePrices = {
            monthly: 450, // INR (discounted from 900)
            annual: 350,  // INR (discounted from 700)
            monthlyOriginal: 900, // INR (original price)
            annualOriginal: 700   // INR (original price)
        };
        
        this.supportedCurrencies = {
            'IN': { code: 'INR', symbol: '₹', flag: '🇮🇳', name: 'Indian Rupee' },
            'US': { code: 'USD', symbol: '$', flag: '🇺🇸', name: 'US Dollar' },
            'GB': { code: 'GBP', symbol: '£', flag: '🇬🇧', name: 'British Pound' },
            'EU': { code: 'EUR', symbol: '€', flag: '🇪🇺', name: 'Euro' },
            'CA': { code: 'CAD', symbol: 'C$', flag: '🇨🇦', name: 'Canadian Dollar' },
            'AU': { code: 'AUD', symbol: 'A$', flag: '🇦🇺', name: 'Australian Dollar' },
            'SG': { code: 'SGD', symbol: 'S$', flag: '🇸🇬', name: 'Singapore Dollar' },
            'JP': { code: 'JPY', symbol: '¥', flag: '🇯🇵', name: 'Japanese Yen' },
            'DE': { code: 'EUR', symbol: '€', flag: '🇩🇪', name: 'Euro' },
            'FR': { code: 'EUR', symbol: '€', flag: '🇫🇷', name: 'Euro' },
            'IT': { code: 'EUR', symbol: '€', flag: '🇮🇹', name: 'Euro' },
            'ES': { code: 'EUR', symbol: '€', flag: '🇪🇸', name: 'Euro' },
            'NL': { code: 'EUR', symbol: '€', flag: '🇳🇱', name: 'Euro' },
            'CH': { code: 'CHF', symbol: 'CHF', flag: '🇨🇭', name: 'Swiss Franc' },
            'SE': { code: 'SEK', symbol: 'kr', flag: '🇸🇪', name: 'Swedish Krona' },
            'NO': { code: 'NOK', symbol: 'kr', flag: '🇳🇴', name: 'Norwegian Krone' },
            'DK': { code: 'DKK', symbol: 'kr', flag: '🇩🇰', name: 'Danish Krone' },
            'BR': { code: 'BRL', symbol: 'R$', flag: '🇧🇷', name: 'Brazilian Real' },
            'MX': { code: 'MXN', symbol: '$', flag: '🇲🇽', name: 'Mexican Peso' },
            'AR': { code: 'ARS', symbol: '$', flag: '🇦🇷', name: 'Argentine Peso' },
            'ZA': { code: 'ZAR', symbol: 'R', flag: '🇿🇦', name: 'South African Rand' },
            'KR': { code: 'KRW', symbol: '₩', flag: '🇰🇷', name: 'South Korean Won' },
            'CN': { code: 'CNY', symbol: '¥', flag: '🇨🇳', name: 'Chinese Yuan' },
            'HK': { code: 'HKD', symbol: 'HK$', flag: '🇭🇰', name: 'Hong Kong Dollar' },
            'TW': { code: 'TWD', symbol: 'NT$', flag: '🇹🇼', name: 'Taiwan Dollar' },
            'TH': { code: 'THB', symbol: '฿', flag: '🇹🇭', name: 'Thai Baht' },
            'MY': { code: 'MYR', symbol: 'RM', flag: '🇲🇾', name: 'Malaysian Ringgit' },
            'ID': { code: 'IDR', symbol: 'Rp', flag: '🇮🇩', name: 'Indonesian Rupiah' },
            'PH': { code: 'PHP', symbol: '₱', flag: '🇵🇭', name: 'Philippine Peso' },
            'VN': { code: 'VND', symbol: '₫', flag: '🇻🇳', name: 'Vietnamese Dong' },
            'AE': { code: 'AED', symbol: 'د.إ', flag: '🇦🇪', name: 'UAE Dirham' },
            'SA': { code: 'SAR', symbol: '﷼', flag: '🇸🇦', name: 'Saudi Riyal' },
            'IL': { code: 'ILS', symbol: '₪', flag: '🇮🇱', name: 'Israeli Shekel' },
            'TR': { code: 'TRY', symbol: '₺', flag: '🇹🇷', name: 'Turkish Lira' },
            'RU': { code: 'RUB', symbol: '₽', flag: '🇷🇺', name: 'Russian Ruble' },
            'PL': { code: 'PLN', symbol: 'zł', flag: '🇵🇱', name: 'Polish Zloty' },
            'CZ': { code: 'CZK', symbol: 'Kč', flag: '🇨🇿', name: 'Czech Koruna' },
            'HU': { code: 'HUF', symbol: 'Ft', flag: '🇭🇺', name: 'Hungarian Forint' },
            'RO': { code: 'RON', symbol: 'lei', flag: '🇷🇴', name: 'Romanian Leu' },
            'BG': { code: 'BGN', symbol: 'лв', flag: '🇧🇬', name: 'Bulgarian Lev' },
            'HR': { code: 'HRK', symbol: 'kn', flag: '🇭🇷', name: 'Croatian Kuna' },
            'RS': { code: 'RSD', symbol: 'дин', flag: '🇷🇸', name: 'Serbian Dinar' },
            'UA': { code: 'UAH', symbol: '₴', flag: '🇺🇦', name: 'Ukrainian Hryvnia' },
            'EG': { code: 'EGP', symbol: '£', flag: '🇪🇬', name: 'Egyptian Pound' },
            'NG': { code: 'NGN', symbol: '₦', flag: '🇳🇬', name: 'Nigerian Naira' },
            'KE': { code: 'KES', symbol: 'KSh', flag: '🇰🇪', name: 'Kenyan Shilling' },
            'GH': { code: 'GHS', symbol: '₵', flag: '🇬🇭', name: 'Ghanaian Cedi' },
            'BD': { code: 'BDT', symbol: '৳', flag: '🇧🇩', name: 'Bangladeshi Taka' },
            'PK': { code: 'PKR', symbol: '₨', flag: '🇵🇰', name: 'Pakistani Rupee' },
            'LK': { code: 'LKR', symbol: '₨', flag: '🇱🇰', name: 'Sri Lankan Rupee' },
            'NP': { code: 'NPR', symbol: '₨', flag: '🇳🇵', name: 'Nepalese Rupee' }
        };
        
        this.exchangeRates = {};
        this.currentCurrency = null;
        this.userCountry = null;
        this.isLoading = false;
        
        // Cache settings
        this.cacheKey = 'stashy_exchange_rates';
        this.cacheExpiry = 4 * 60 * 60 * 1000; // 4 hours
        
        this.init();
    }
    
    async init() {
        try {
            this.isLoading = true;
            this.showLoadingState();

            // Check for user's preferred currency first
            const preferredCurrency = localStorage.getItem('stashy_preferred_currency');
            if (preferredCurrency && this.supportedCurrencies[preferredCurrency]) {
                this.userCountry = preferredCurrency;
                this.currentCurrency = this.getCurrencyForCountry(preferredCurrency);
            }

            // Try to get cached data
            const cachedData = this.getCachedRates();
            if (cachedData) {
                this.exchangeRates = cachedData.rates;
                if (!this.userCountry) {
                    this.userCountry = cachedData.country;
                    this.currentCurrency = this.getCurrencyForCountry(this.userCountry);
                }

                // Update pricing immediately with cached data
                this.updateAllPricing();
                this.createCurrencySelector();
                this.hideLoadingState();
                this.isLoading = false;
                this.dispatchCurrencyEvent('initialized');

                // Continue with background updates
                this.backgroundUpdate();
                return;
            }

            // No cache available, do full initialization
            await this.fullInitialization();

        } catch (error) {
            console.error('Currency detection failed:', error);
            this.fallbackToINR();
        }
    }

    async fullInitialization() {
        try {
            // Detect user location if not already set
            if (!this.userCountry) {
                await this.detectUserLocation();
            }

            // Get exchange rates
            await this.fetchExchangeRates();

            // Update pricing display
            this.updateAllPricing();

            // Create currency selector
            this.createCurrencySelector();

            this.isLoading = false;
            this.hideLoadingState();
            this.dispatchCurrencyEvent('initialized');

        } catch (error) {
            console.error('Full initialization failed:', error);
            this.fallbackToINR();
        }
    }

    async backgroundUpdate() {
        try {
            // Update location and rates in background
            const promises = [];

            if (!this.userCountry) {
                promises.push(this.detectUserLocation());
            }

            // Check if rates need updating (older than 2 hours)
            const cachedData = this.getCachedRates();
            if (!cachedData || Date.now() - cachedData.timestamp > 2 * 60 * 60 * 1000) {
                promises.push(this.fetchExchangeRates());
            }

            await Promise.allSettled(promises);

            // Update display if anything changed
            this.updateAllPricing();

        } catch (error) {
            console.warn('Background update failed:', error);
            // Don't fallback here, just log the error
        }
    }
    
    showLoadingState() {
        // Add loading indicators to price elements
        const priceElements = document.querySelectorAll('.price');
        priceElements.forEach(el => {
            el.style.opacity = '0.6';
        });
    }
    
    hideLoadingState() {
        const priceElements = document.querySelectorAll('.price');
        priceElements.forEach(el => {
            el.style.opacity = '1';
        });
    }
    
    async detectUserLocation() {
        try {
            // Try multiple geolocation services for reliability
            const services = [
                { url: 'https://ipapi.co/json/', timeout: 3000 },
                { url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free', timeout: 3000 },
                { url: 'https://ipinfo.io/json', timeout: 3000 },
                { url: 'https://httpbin.org/ip', timeout: 2000 } // Fallback for basic IP
            ];

            for (const service of services) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), service.timeout);

                    const response = await fetch(service.url, {
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();

                    // Extract country code from different API formats
                    const countryCode = data.country_code || data.country || data.countryCode;

                    if (countryCode && this.supportedCurrencies[countryCode]) {
                        this.userCountry = countryCode;
                        this.currentCurrency = this.getCurrencyForCountry(countryCode);
                        console.log(`Location detected: ${countryCode} via ${service.url}`);
                        break;
                    }
                } catch (error) {
                    console.warn(`Geolocation service failed: ${service.url}`, error.message);
                    continue;
                }
            }

            // Fallback to India if detection fails
            if (!this.userCountry) {
                this.userCountry = 'IN';
                this.currentCurrency = this.supportedCurrencies['IN'];
                console.log('Location detection failed, defaulting to India');
            }

        } catch (error) {
            console.error('Location detection failed:', error);
            this.userCountry = 'IN';
            this.currentCurrency = this.supportedCurrencies['IN'];
        }
    }
    
    getCurrencyForCountry(countryCode) {
        return this.supportedCurrencies[countryCode] || this.supportedCurrencies['IN'];
    }
    
    async fetchExchangeRates() {
        try {
            // Use multiple exchange rate APIs for reliability
            const apis = [
                { url: 'https://api.exchangerate-api.com/v4/latest/INR', timeout: 5000 },
                { url: 'https://open.er-api.com/v6/latest/INR', timeout: 5000 },
                { url: 'https://api.fixer.io/latest?base=INR&access_key=free', timeout: 5000 }
            ];

            for (const api of apis) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), api.timeout);

                    const response = await fetch(api.url, {
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.rates && typeof data.rates === 'object') {
                        // Validate that we have reasonable rates
                        const usdRate = data.rates.USD;
                        if (usdRate && usdRate > 0.005 && usdRate < 0.02) { // Sanity check for USD rate
                            this.exchangeRates = data.rates;
                            this.cacheRates();
                            console.log(`Exchange rates fetched from: ${api.url}`);
                            return;
                        }
                    }
                } catch (error) {
                    console.warn(`Exchange rate API failed: ${api.url}`, error.message);
                    continue;
                }
            }

            // Fallback rates if all APIs fail
            console.warn('All exchange rate APIs failed, using fallback rates');
            this.setFallbackRates();

        } catch (error) {
            console.error('Exchange rate fetch failed:', error);
            this.setFallbackRates();
        }
    }
    
    setFallbackRates() {
        // Approximate exchange rates as fallback
        this.exchangeRates = {
            'USD': 0.012,
            'EUR': 0.011,
            'GBP': 0.0095,
            'CAD': 0.016,
            'AUD': 0.018,
            'SGD': 0.016,
            'JPY': 1.8,
            'CHF': 0.011,
            'SEK': 0.13,
            'NOK': 0.13,
            'DKK': 0.082,
            'BRL': 0.067,
            'MXN': 0.24,
            'ARS': 12.5,
            'ZAR': 0.22,
            'KRW': 16.2,
            'CNY': 0.087,
            'HKD': 0.094,
            'TWD': 0.38,
            'THB': 0.42,
            'MYR': 0.056,
            'IDR': 185,
            'PHP': 0.68,
            'VND': 295,
            'AED': 0.044,
            'SAR': 0.045,
            'ILS': 0.045,
            'TRY': 0.35,
            'RUB': 1.2,
            'PLN': 0.049,
            'CZK': 0.28,
            'HUF': 4.4,
            'RON': 0.055,
            'BGN': 0.022,
            'HRK': 0.083,
            'RSD': 1.3,
            'UAH': 0.49,
            'EGP': 0.59,
            'NGN': 18.5,
            'KES': 1.9,
            'GHS': 0.15,
            'BDT': 1.3,
            'PKR': 3.4,
            'LKR': 3.6,
            'NPR': 1.6
        };
    }

    cacheRates() {
        const cacheData = {
            rates: this.exchangeRates,
            country: this.userCountry,
            timestamp: Date.now()
        };
        localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
    }

    getCachedRates() {
        try {
            const cached = localStorage.getItem(this.cacheKey);
            if (!cached) return null;

            const data = JSON.parse(cached);
            const isExpired = Date.now() - data.timestamp > this.cacheExpiry;

            if (isExpired) {
                localStorage.removeItem(this.cacheKey);
                return null;
            }

            return data;
        } catch (error) {
            console.error('Cache read error:', error);
            return null;
        }
    }

    convertPrice(priceINR, targetCurrency) {
        if (targetCurrency === 'INR') return priceINR;

        const rate = this.exchangeRates[targetCurrency];
        if (!rate) return priceINR;

        const convertedPrice = priceINR * rate;

        // Round to appropriate decimal places based on currency
        if (['JPY', 'KRW', 'VND', 'IDR'].includes(targetCurrency)) {
            return Math.round(convertedPrice); // No decimals for these currencies
        } else {
            return Math.round(convertedPrice * 100) / 100; // 2 decimal places
        }
    }

    formatPrice(amount, currencyInfo) {
        // Handle null currencyInfo by falling back to INR
        if (!currencyInfo) {
            currencyInfo = this.supportedCurrencies['IN'];
        }

        const { symbol, code } = currencyInfo;

        // Format based on currency conventions
        if (['JPY', 'KRW', 'VND', 'IDR'].includes(code)) {
            return `${symbol}${amount.toLocaleString()}`;
        } else {
            return `${symbol}${amount.toFixed(2)}`;
        }
    }

    updateAllPricing() {
        if (!this.currentCurrency) return;

        const monthlyPrice = this.convertPrice(this.basePrices.monthly, this.currentCurrency.code);
        const annualPrice = this.convertPrice(this.basePrices.annual, this.currentCurrency.code);
        const monthlyOriginalPrice = this.convertPrice(this.basePrices.monthlyOriginal, this.currentCurrency.code);
        const annualOriginalPrice = this.convertPrice(this.basePrices.annualOriginal, this.currentCurrency.code);

        // Update pricing cards
        this.updatePricingCards(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice);

        // Update comparison table
        this.updateComparisonTable();

        // Update FAQ section
        this.updateFAQPricing(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice);

        // Update meta descriptions if needed
        this.updateMetaDescriptions(monthlyPrice, annualPrice);
    }

    updatePricingCards(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice) {
        if (!this.currentCurrency) return;

        // Update monthly prices
        const monthlyPriceEl = document.querySelector('.monthly-price');
        if (monthlyPriceEl) {
            monthlyPriceEl.textContent = this.formatPrice(monthlyPrice, this.currentCurrency);
        }

        const monthlyOriginalEl = document.querySelector('.monthly-original');
        if (monthlyOriginalEl) {
            monthlyOriginalEl.textContent = this.formatPrice(monthlyOriginalPrice, this.currentCurrency);
        }

        // Update annual prices
        const annualPriceEl = document.querySelector('.annual-price');
        if (annualPriceEl) {
            annualPriceEl.textContent = this.formatPrice(annualPrice, this.currentCurrency);
        }

        const annualOriginalEl = document.querySelector('.annual-original');
        if (annualOriginalEl) {
            annualOriginalEl.textContent = this.formatPrice(annualOriginalPrice, this.currentCurrency);
        }

        // Update free plan price (always 0)
        const freePriceEl = document.querySelector('.free-price');
        if (freePriceEl) {
            freePriceEl.textContent = `${this.currentCurrency.symbol}0`;
        }

        // Add currency indicators to pricing cards
        this.addCurrencyIndicators();

        // Update savings calculation
        this.updateSavingsCalculation(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice);
    }

    addCurrencyIndicators() {
        if (!this.currentCurrency) return;

        const pricingCards = document.querySelectorAll('.pricing-card');
        pricingCards.forEach(card => {
            // Remove existing indicators
            const existingIndicator = card.querySelector('.currency-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            // Add new indicator if not INR
            if (this.currentCurrency.code !== 'INR') {
                const priceElement = card.querySelector('.plan-price');
                if (priceElement) {
                    const indicator = document.createElement('div');
                    indicator.className = 'currency-indicator';
                    indicator.innerHTML = `
                        <span class="flag">${this.currentCurrency.flag}</span>
                        <span>${this.currentCurrency.code}</span>
                    `;
                    priceElement.appendChild(indicator);
                }
            }
        });
    }

    updateComparisonTable() {
        // Update any pricing mentions in the comparison table
        // This is mainly for future use if we add pricing details in the table
    }

    updateFAQPricing(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice) {
        // Calculate annual savings
        const annualTotal = annualPrice * 12;
        const monthlyTotal = monthlyPrice * 12;
        const savings = monthlyTotal - annualTotal;

        // Update FAQ content with discount pricing
        const faqPricingHighlight = document.querySelector('.faq-pricing-highlight');
        if (faqPricingHighlight) {
            const monthlyOriginalFormatted = this.formatPrice(monthlyOriginalPrice, this.currentCurrency);
            const monthlyFormatted = this.formatPrice(monthlyPrice, this.currentCurrency);
            const annualOriginalFormatted = this.formatPrice(annualOriginalPrice, this.currentCurrency);
            const annualFormatted = this.formatPrice(annualPrice, this.currentCurrency);
            const savingsFormatted = this.formatPrice(savings, this.currentCurrency);

            faqPricingHighlight.innerHTML = `
                <p><strong>Amazing savings with our current promotion!</strong></p>
                <p>Monthly: <span class="original-price">${monthlyOriginalFormatted}</span> <span class="current-price">${monthlyFormatted}/month</span> (50% OFF)</p>
                <p>Annual: <span class="original-price">${annualOriginalFormatted}</span> <span class="current-price">${annualFormatted}/month</span> (50% OFF + additional annual savings)</p>
                <p>Choose annual billing and save <strong>${savingsFormatted} per year</strong> compared to monthly, plus get our 50% new user discount!</p>
            `;
        }
    }

    updateSavingsCalculation(monthlyPrice, annualPrice, monthlyOriginalPrice, annualOriginalPrice) {
        // Calculate discount percentages and savings
        const monthlyDiscount = Math.round(((monthlyOriginalPrice - monthlyPrice) / monthlyOriginalPrice) * 100);
        const annualDiscount = Math.round(((annualOriginalPrice - annualPrice) / annualOriginalPrice) * 100);

        // Update discount badges if they exist
        const discountBadge = document.querySelector('.discount-badge');
        if (discountBadge) {
            discountBadge.textContent = `${monthlyDiscount}% OFF`;
        }

        // Add savings highlights to pricing cards
        const pricingCard = document.querySelector('.pricing-card.featured');
        if (pricingCard) {
            let savingsHighlight = pricingCard.querySelector('.savings-highlight');
            if (!savingsHighlight) {
                savingsHighlight = document.createElement('div');
                savingsHighlight.className = 'savings-highlight';
                const cardFooter = pricingCard.querySelector('.card-footer');
                if (cardFooter) {
                    cardFooter.parentNode.insertBefore(savingsHighlight, cardFooter);
                }
            }

            const monthlySavings = monthlyOriginalPrice - monthlyPrice;
            const annualSavings = (monthlyOriginalPrice * 12) - (annualPrice * 12);
            const monthlySavingsFormatted = this.formatPrice(monthlySavings, this.currentCurrency);
            const annualSavingsFormatted = this.formatPrice(annualSavings, this.currentCurrency);

            savingsHighlight.innerHTML = `
                <div class="savings-amount">Save ${monthlySavingsFormatted}/month</div>
                <div class="savings-text">Or ${annualSavingsFormatted}/year with annual plan</div>
            `;
        }
    }

    updateMetaDescriptions(monthlyPrice, annualPrice) {
        // Update meta descriptions with localized pricing
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc && this.currentCurrency && this.currentCurrency.code !== 'INR') {
            const monthlyFormatted = this.formatPrice(monthlyPrice, this.currentCurrency);
            metaDesc.content = `Choose the perfect Stashy plan for your knowledge journey. Free plan available with 2 notebooks and 10 notes. Premium plan for ${monthlyFormatted}/month with unlimited features and 7-day free trial.`;
        }
    }

    createCurrencySelector() {
        if (!this.currentCurrency) return;

        // Create currency selector dropdown
        const selectorHTML = `
            <div class="currency-selector">
                <button class="currency-button" id="currency-button">
                    <span class="currency-flag">${this.currentCurrency.flag}</span>
                    <span class="currency-code">${this.currentCurrency.code}</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="currency-dropdown" id="currency-dropdown">
                    ${this.generateCurrencyOptions()}
                </div>
            </div>
        `;

        // Add to pricing toggle section
        const toggleSection = document.querySelector('.pricing-toggle');
        if (toggleSection) {
            toggleSection.insertAdjacentHTML('afterend', selectorHTML);
            this.attachCurrencyEvents();
        }
    }

    generateCurrencyOptions() {
        const popularCurrencies = ['IN', 'US', 'GB', 'EU', 'CA', 'AU', 'SG', 'JP'];

        // Get unique currencies for "Other" section, excluding popular ones and duplicates
        const seenCurrencies = new Set();
        const otherCurrencies = Object.keys(this.supportedCurrencies)
            .filter(code => {
                if (popularCurrencies.includes(code)) return false;
                const currencyCode = this.supportedCurrencies[code].code;
                if (seenCurrencies.has(currencyCode)) return false;
                seenCurrencies.add(currencyCode);
                return true;
            })
            .sort((a, b) => this.supportedCurrencies[a].name.localeCompare(this.supportedCurrencies[b].name));

        let optionsHTML = '<div class="currency-section"><h4>Popular Currencies</h4>';

        popularCurrencies.forEach(countryCode => {
            const currency = this.supportedCurrencies[countryCode];
            optionsHTML += `
                <div class="currency-option" data-country="${countryCode}" title="${currency.name} (${currency.code})">
                    <span class="currency-flag">${currency.flag}</span>
                    <div class="currency-info">
                        <div class="currency-name">${currency.name}</div>
                        <div class="currency-code">${currency.code}</div>
                    </div>
                </div>
            `;
        });

        optionsHTML += '</div><div class="currency-section"><h4>Other Currencies</h4>';

        otherCurrencies.forEach(countryCode => {
            const currency = this.supportedCurrencies[countryCode];
            optionsHTML += `
                <div class="currency-option" data-country="${countryCode}" title="${currency.name} (${currency.code})">
                    <span class="currency-flag">${currency.flag}</span>
                    <div class="currency-info">
                        <div class="currency-name">${currency.name}</div>
                        <div class="currency-code">${currency.code}</div>
                    </div>
                </div>
            `;
        });

        optionsHTML += '</div>';
        return optionsHTML;
    }

    attachCurrencyEvents() {
        const button = document.getElementById('currency-button');
        const dropdown = document.getElementById('currency-dropdown');
        const options = dropdown.querySelectorAll('.currency-option');

        // Toggle dropdown
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdown.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('show');
        });

        // Handle currency selection
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const countryCode = option.dataset.country;
                this.changeCurrency(countryCode);
                dropdown.classList.remove('show');
            });
        });
    }

    changeCurrency(countryCode) {
        this.userCountry = countryCode;
        this.currentCurrency = this.getCurrencyForCountry(countryCode);

        // Update button display
        const button = document.getElementById('currency-button');
        if (button) {
            button.querySelector('.currency-flag').textContent = this.currentCurrency.flag;
            button.querySelector('.currency-code').textContent = this.currentCurrency.code;
        }

        // Update all pricing
        this.updateAllPricing();

        // Cache the user's preference
        localStorage.setItem('stashy_preferred_currency', countryCode);

        // Track currency change for analytics
        if (window.gtag) {
            gtag('event', 'currency_change', {
                'currency': this.currentCurrency.code,
                'country': countryCode
            });
        }

        // Dispatch currency change event
        this.dispatchCurrencyEvent('changed');
    }

    fallbackToINR() {
        this.userCountry = 'IN';
        this.currentCurrency = this.supportedCurrencies['IN'];
        this.exchangeRates = {}; // Empty rates means no conversion needed
        this.updateAllPricing();
        this.createCurrencySelector();
        this.isLoading = false;
        this.hideLoadingState();
        this.dispatchCurrencyEvent('initialized');
    }

    // Public method to get current pricing for payment integration
    getCurrentPricing() {
        if (!this.currentCurrency) {
            return {
                monthly: this.basePrices.monthly,
                annual: this.basePrices.annual,
                monthlyOriginal: this.basePrices.monthlyOriginal,
                annualOriginal: this.basePrices.annualOriginal,
                currency: 'INR',
                symbol: '₹'
            };
        }

        return {
            monthly: this.convertPrice(this.basePrices.monthly, this.currentCurrency.code),
            annual: this.convertPrice(this.basePrices.annual, this.currentCurrency.code),
            monthlyOriginal: this.convertPrice(this.basePrices.monthlyOriginal, this.currentCurrency.code),
            annualOriginal: this.convertPrice(this.basePrices.annualOriginal, this.currentCurrency.code),
            currency: this.currentCurrency.code,
            symbol: this.currentCurrency.symbol
        };
    }

    dispatchCurrencyEvent(type) {
        // Dispatch custom event for other components to listen to
        const event = new CustomEvent('currencyDetectorUpdate', {
            detail: {
                type: type, // 'initialized' or 'changed'
                currency: this.currentCurrency,
                country: this.userCountry,
                pricing: this.getCurrentPricing()
            }
        });
        document.dispatchEvent(event);
    }
}

// Initialize currency detector when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.currencyDetector = new CurrencyDetector();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CurrencyDetector;
}
